# Form State Restoration Test

## Test Scenario:
1. Go to Landing page
2. Select:
   - Departure date: "25 Dec"
   - Class: "Economy Class"
   - Adults: 2
   - Children: 1
   - Infants: 1
3. Click "Search Flights"
4. Navigate back to Landing page
5. Verify all values are restored

## Expected URL when searching:
```
/flights/listing?fromCity=Bangalore&fromCode=BLR&fromAirport=Bangalore%20International%20Airport&toCity=New%20Delhi&toCode=DEL&toAirport=Indira%20Gandhi%20International%20Airport&departure=25%20Dec&passengers=4&class=Economy%20Class&adults=2&children=1&infants=1
```

## Expected Behavior:
- When navigating back, the Landing page should read these query parameters
- Form should automatically restore:
  - Departure date: "25 Dec"
  - Class: "Economy Class" 
  - Adults: 2
  - Children: 1
  - Infants: 1
  - Total passengers: 4

## Code Flow:
1. `Landing.svelte` onMount() calls `restoreFormStateFromQueryParams()`
2. Function reads URL params and populates `formState`
3. `formState` is passed to `FlightSearchForm`
4. Reactive statements in `FlightSearchForm` update form variables
5. TravellerClassModal shows correct initial values
