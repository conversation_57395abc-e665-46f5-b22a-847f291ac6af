// Interface for the flight search parameters
export interface FlightSearchParams {
	source: SourceAirport;
	destination: DestinationAirport;
	sourceCode: string;
	destCode: string;
	departureDate: string;       
	returnDate?: string;         
	passengers: Passengers;
	travellerClass: TravellerClass;
}

export interface Airport{
    city:string; 
    iataCode:string;
    countryCode?:string;
}

export interface SourceAirport extends Airport{
    countryCode:string;
    fareType?: string;     
	partnerCountry?: string; 
}

export interface DestinationAirport extends Airport{
    countryCode:string;
    fareType?: string;      
	partnerCountry?: string; 
}

export interface Passengers{
    adults:number;
    children?:number;
    infants?:number;
}

export interface TravellerClass {
	displayName: 'Economy Class' | 'Business Class' | 'Premium Class';
	apiKey: 'ECONOMY' | 'BUSINESS' | 'PREMIUM';
}

export interface FlightSearchResponse {
	flights: TransformedFlight[];
	hasError: boolean;
	error?: string;
}

export interface TransformedFlight
{
    id: string | number;
    airline: string;
    logo: string;
    departure: string;
    arrival: string;
    departureAirport: string;
    arrivalAirport: string;
    duration: string;
    price: number;
    originalPrice?: number;
    offer?: string;
    tags: string[];
    amenities: any[];
    _originalData: any;
}