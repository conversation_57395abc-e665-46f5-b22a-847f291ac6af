<script lang="ts">
	import { createEventDispatcher } from "svelte";
	import { openBottomSheet } from '@CDNA-Technologies/svelte-vitals/components/bottom-sheet';
	
	export let selectedClass;
	export let travellers;
	
	const dispatch = createEventDispatcher();
	
	function openModal() {
		openBottomSheet('traveller-selection');
	}
</script>

<!-- Class and Travellers -->
<div class="w-full flex bg-white border border-gray-200 rounded-lg shadow-sm text-left mb-4">
	<div
		class="cursor-pointer flex-1 p-4 px-4"
		on:click|stopPropagation={openModal}
	>
		<div class="flex items-center">
			<div>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					width="24"
					height="24"
					viewBox="0 0 24 24"
					fill="none"
				>
					<path
						d="M20.9996 19.001C20.7921 19.2087 7.32259 19.3234 6.99962 19.001C6.67666 18.6786 2.24972 4.50034 2.49943 4C2.74914 3.49966 6.26555 2.75713 6.99962 3.00097C7.7337 3.24481 9.99936 14.501 10.4996 14.501C10.9999 14.501 20.7153 14.2169 20.9996 14.501C21.284 14.7851 21.2072 18.7933 20.9996 19.001Z"
						stroke="#8A8A8A"
						stroke-width="1.5"
					/>
					<path d="M9 9H18" stroke="#8A8A8A" stroke-width="1.8" stroke-linecap="round" />
					<path d="M9 22L19 22" stroke="#8A8A8A" stroke-width="1.8" stroke-linecap="round" />
				</svg>
			</div>
			<div class="pl-3">
				<div class="text-gray-500 text-xs">Class</div>
				<div class="select-none text-gray-900 font-bold text-lg flex items-center">
					{selectedClass}
					<span class="select-none text-gray-500 text-xl ml-1">&#9660;</span>
				</div>
			</div>
		</div>
	</div>
	
	<div class="pr-8 border-l border-gray-200 my-2" />
	<!-- Vertical divider -->
	
	<div
		class="cursor-pointer flex-1 p-4 px-4"
		on:click|stopPropagation={openModal}
	>
		<div class="flex items-center">
			<div>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					width="24"
					height="24"
					viewBox="0 0 24 24"
					fill="none"
				>
					<path
						d="M18 21C18 18.8783 17.1571 16.8434 15.6569 15.3431C14.1566 13.8429 12.1217 13 10 13C7.87827 13 5.84344 13.8429 4.34315 15.3431C2.84285 16.8434 2 18.8783 2 21"
						stroke="#8A8A8A"
						stroke-width="1.5"
						stroke-linecap="round"
						stroke-linejoin="round"
					/>
					<path
						d="M10 13C12.7614 13 15 10.7614 15 8C15 5.23858 12.7614 3 10 3C7.23858 3 5 5.23858 5 8C5 10.7614 7.23858 13 10 13Z"
						stroke="#8A8A8A"
						stroke-width="1.5"
						stroke-linecap="round"
						stroke-linejoin="round"
					/>
					<path
						d="M22.0008 19.9992C22.0008 16.6292 20.0008 13.4992 18.0008 11.9992C18.6582 11.506 19.1839 10.8583 19.5313 10.1135C19.8788 9.36867 20.0373 8.54969 19.9928 7.72902C19.9483 6.90835 19.7022 6.1113 19.2763 5.40842C18.8503 4.70553 18.2577 4.11848 17.5508 3.69922"
						stroke="#8A8A8A"
						stroke-width="1.5"
						stroke-linecap="round"
						stroke-linejoin="round"
					/>
				</svg>
			</div>
			<div class="pl-3">
				<div class="select-none text-gray-500 text-xs">Traveller(s)</div>
				<div class="select-none text-gray-900 font-bold text-lg flex items-center">
					{travellers}
					<span class="select-none text-gray-500 text-xl ml-1">&#9660;</span>
				</div>
			</div>
		</div>
	</div>
</div>