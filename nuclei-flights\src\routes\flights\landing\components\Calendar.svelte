<script lang="ts">
	import { onMount, onDestroy, createEventDispatcher } from 'svelte';
	import { NucleiLogger } from '@CDNA-Technologies/svelte-vitals/logger';
  import PrimaryButton from "@CDNA-Technologies/svelte-vitals/components/primary-button";

	// Props
	export let isOpen = false;
	export let selectedDate: Date | null = null;
	export let position: 'bottom' | 'top' = 'bottom';

	// Event dispatcher
	const dispatch = createEventDispatcher<{
		select: Date;
		close: void;
	}>();

	// Calendar state
	let currentMonth = new Date();
	let calendarElement: HTMLElement;

	// Initialize current month based on selected date
	$: if (isOpen && selectedDate) {
		currentMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1);
	} else if (isOpen && !selectedDate) {
		currentMonth = new Date();
	}

	// Calendar helper functions
	function getDaysInMonth(date: Date): Date[] {
		const year = date.getFullYear();
		const month = date.getMonth();
		const firstDay = new Date(year, month, 1);
		const startDate = new Date(firstDay);

		// Start from Sunday of the first week
		startDate.setDate(firstDay.getDate() - firstDay.getDay());

		const days: Date[] = [];
		const currentDate = new Date(startDate);

		// Generate 42 days (6 weeks) to fill the calendar grid
		for (let i = 0; i < 42; i++) {
			days.push(new Date(currentDate));
			currentDate.setDate(currentDate.getDate() + 1);
		}

		return days;
	}

	function isToday(date: Date): boolean {
		const today = new Date();
		return date.toDateString() === today.toDateString();
	}

	function isSelected(date: Date): boolean {
		return selectedDate ? date.toDateString() === selectedDate.toDateString() : false;
	}

	function isCurrentMonth(date: Date): boolean {
		return (
			date.getMonth() === currentMonth.getMonth() &&
			date.getFullYear() === currentMonth.getFullYear()
		);
	}

	function isPastDate(date: Date): boolean {
		const today = new Date();
		today.setHours(0, 0, 0, 0);
		return date < today;
	}

	function selectDate(date: Date) {
		if (isPastDate(date)) return;

		dispatch('select', date);
	}

	function closeCalendar() {
		dispatch('close');
	}

	function previousMonth() {
		currentMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1);
	}

	function nextMonth() {
		currentMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1);
	}

	// Handle click outside
	function handleClickOutside(event: MouseEvent) {
		if (isOpen && calendarElement && !calendarElement.contains(event.target as Node)) {
			closeCalendar();
		}
	}

	onMount(() => {
		document.addEventListener('click', handleClickOutside);
	});

	onDestroy(() => {
		document.removeEventListener('click', handleClickOutside);
	});

	// Reactive statements
	$: calendarDays = getDaysInMonth(currentMonth);
	$: monthName = currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
</script>

{#if isOpen}
	<!-- Calendar Container with better positioning -->
	<div
		bind:this={calendarElement}
		class="absolute left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-2xl p-4 min-w-[320px] max-w-[400px]"
		style="
            z-index: 9999;
            {position === 'top'
			? 'bottom: 100%; margin-bottom: 8px;'
			: 'top: 100%; margin-top: 8px;'}
        "
	>
		<!-- Calendar Header -->
		<div class="flex items-center justify-between mb-4">
			<button
				type="button"
				class="p-2 hover:bg-gray-100 rounded-full transition-colors"
				on:click={previousMonth}
			>
				<svg
					width="16"
					height="16"
					viewBox="0 0 24 24"
					fill="none"
					stroke="currentColor"
					stroke-width="2"
				>
					<path d="M15 18L9 12L15 6" />
				</svg>
			</button>

			<h3 class="text-lg font-semibold text-gray-900">{monthName}</h3>

			<button
				type="button"
				class="p-2 hover:bg-gray-100 rounded-full transition-colors"
				on:click={nextMonth}
			>
				<svg
					width="16"
					height="16"
					viewBox="0 0 24 24"
					fill="none"
					stroke="currentColor"
					stroke-width="2"
				>
					<path d="M9 18L15 12L9 6" />
				</svg>
			</button>
		</div>

		<!-- Weekday Headers -->
		<div class="grid grid-cols-7 gap-1 mb-2">
			{#each ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'] as day}
				<div class="text-center text-xs font-medium text-gray-500 py-2">
					{day}
				</div>
			{/each}
		</div>

		<!-- Calendar Days -->
		<div class="grid grid-cols-7 gap-1">
			{#each calendarDays as day}
				<button
					type="button"
					class="
                        aspect-square p-2 text-sm rounded-lg transition-all duration-200 flex items-center justify-center
                        {isCurrentMonth(day) ? 'text-gray-900 hover:bg-blue-50' : 'text-gray-300'}
                        {isSelected(day) ? 'bg-blue-500 text-white hover:bg-blue-600' : ''}
                        {isToday(day) && !isSelected(day)
						? 'bg-blue-100 text-blue-600 font-semibold'
						: ''}
                        {isPastDate(day)
						? 'text-gray-300 cursor-not-allowed hover:bg-transparent'
						: 'cursor-pointer'}
                    "
					on:click={() => selectDate(day)}
					disabled={isPastDate(day)}
				>
					{day.getDate()}
				</button>
			{/each}
		</div>

		<!-- Calendar Footer -->
		<div class="flex justify-end mt-4 pt-4 border-t border-gray-200">
			<button
				type="button"
				class="px-3 py-1.5 text-sm text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
				on:click={closeCalendar}
			>
				Close
			</button>
		</div>
	</div>
{/if}
