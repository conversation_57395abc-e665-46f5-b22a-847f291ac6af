import { ApiUtil } from '@CDNA-Technologies/svelte-vitals/api-util';
import {
    setLoadingLce,
    setContentLce,
    setErrorLce,
    lceStore
} from '@CDNA-Technologies/svelte-vitals/error-handling';
import { NucleiLogger } from '@CDNA-Technologies/svelte-vitals/logger';
import type { AirportList } from '$lib/flights-commons/messages/flight-popular-cities.msg.ts';
import { fetchPopularCitiesFromAPI }  from '$flights/flight-api.js';

export interface Airport {
    code: string;
    name: string;
    airport: string;
}

// Function to convert API response format to the Airport interface
function convertApiToAirport(apiAirport: AirportList): Airport {
    return {
        code: apiAirport.iataCode,
        name: apiAirport.city,
        airport: apiAirport.name
    };
}

export async function getPopularCities(): Promise<Airport[]> {
    try {
        // This shows loading spinner in UI
        setLoadingLce();

        NucleiLogger.logInfo('Fetching popular cities from API', 'FLIGHTS-API');

        // Make the API call
        const response = await fetchPopularCitiesFromAPI();

        // When API call fails
        if (response.hasError()) {
            const errorMessage =
                response.error?.description || response.error?.title || 'Failed to fetch popular cities';
            NucleiLogger.logException(
                'API error while fetching popular cities:',
                'POPULAR_CITIES_ERROR',
                response.error
            );

            // Set LCE error state with user-friendly message
            setErrorLce({
                title: 'Unable to Load Cities',
                description:
                    "We couldn't load the popular cities list. Please check your internet connection and try again.",
                actionText: 'Retry',
                barrierDismissible: true
            });

            return []; // Return empty array
        }

        // API succeeded but no cities returned
        if (!response.response?.airportList || response.response.airportList.length === 0) {
            NucleiLogger.logWarn('No popular cities found in API response', 'POPULAR_CITIES');

            // Set LCE error state for empty results
            setErrorLce({
                title: 'No Cities Available',
                description: 'No popular cities are available at the moment. Please try again later.',
                actionText: 'Refresh',
                barrierDismissible: true
            });

            return [];
        }

        // Transform the data and set content state
        try {
            NucleiLogger.logDebug('FLIGHTS-API', 'BEFORE CONVERTING', response.response.airportList);
            const transformedAirports = response.response.airportList.map(convertApiToAirport);
            NucleiLogger.logDebug('FLIGHTS-API', 'AFTER CONVERTING', transformedAirports);

            // Validate transformation worked
            if (transformedAirports.length === 0) {
                throw new Error('Data transformation resulted in empty array');
            }

            //Set content state - this hides loading and shows cities
            setContentLce();

            NucleiLogger.logSuccess('Popular cities loaded successfully', 'POPULAR_CITIES', {
                citiesCount: transformedAirports.length,
                sampleCities: transformedAirports.slice(0, 3).map((city) => `${city.name} (${city.code})`)
            });

            return transformedAirports;
        } catch (transformError) {
            // Data transformation failed
            NucleiLogger.logException(
                'Error transforming popular cities data:',
                'DATA_TRANSFORM',
                transformError
            );

            setErrorLce({
                title: 'Data Processing Error',
                description:
                    "We received the cities data but couldn't process it properly. Please try again.",
                actionText: 'Retry',
                barrierDismissible: true
            });

            return [];
        }
    } catch (error) {
        NucleiLogger.logException(
            'Unexpected error in getPopularCities:',
            'POPULAR_CITIES_CRITICAL',
            error
        );

        // Set LCE error state for unexpected failures
        setErrorLce({
            title: 'Connection Error',
            description:
                'Unable to connect to our servers. Please check your internet connection and try again.',
            actionText: 'Try Again',
            barrierDismissible: true
        });

        return []; //return empty array to prevent UI crashes
    }
}

// Export LCE store for component usage
export { lceStore };
