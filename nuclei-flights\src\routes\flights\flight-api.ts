import { ApiUtil } from "@CDNA-Technologies/svelte-vitals/api-util";
import type {
  AirportSuggestionsResponse,
  AirportList,
  AirportSearchRequest,
} from "$lib/flights-commons/messages/airport-search.msg.ts";
import type { FlightSearchParams } from "$lib/flights-commons/messages/flight-search-params.msg.ts";

export async function getAirportSearchResults(searchText: string) {
  const airportSearchRequest: AirportSearchRequest = {
    searchText: searchText.trim(),
  };
  const endpoint =
    "/com.gonuclei.flights.v1.LandingService/getAirportSearchResults";
  const requestBody = airportSearchRequest;
  return await ApiUtil.post<AirportSearchRequest, AirportSuggestionsResponse>(
    endpoint,
    requestBody
  );
}

export async function fetchPopularCitiesFromAPI() {
  const endpoint = "/com.gonuclei.flights.v1.LandingService/getPopularCities";
  const requestBody = {};

  return await ApiUtil.post(endpoint, requestBody);
}

export async function fetchFlightsFromAPI(apiPayload: FlightSearchParams) {
  const endpoint =
    "/com.gonuclei.flights.v1.ListingService/GetFlightsSearchListV2";
  const requestBody = apiPayload;
  return await ApiUtil.post(endpoint, requestBody);
}
