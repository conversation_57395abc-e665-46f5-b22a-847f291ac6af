<script lang="ts">
  // Import required modules
  import { base } from "$app/paths";
  import { ListCouponCta } from "@CDNA-Technologies/svelte-vitals/cart/coupons";
  import { LandingRewardCta } from "@CDNA-Technologies/svelte-vitals/cart/rewards";
  import { LandingWalletCta } from "@CDNA-Technologies/svelte-vitals/cart/wallet";
  import AppBar from "@CDNA-Technologies/svelte-vitals/components/appbar";
  import PrimaryLoader from "@CDNA-Technologies/svelte-vitals/components/primary-loader";
  import ThreeDotMenu from "@CDNA-Technologies/svelte-vitals/components/three-dot-menu";
  import {
    ErrorHandling,
    lceStore,
    setContentLce,
    setLoadingLce,
  } from "@CDNA-Technologies/svelte-vitals/error-handling";
  import { NucleiLogger } from "@CDNA-Technologies/svelte-vitals/logger";
  import { NavigatorUtils } from "@CDNA-Technologies/svelte-vitals/navigator";
  import { onMount } from "svelte";
  import { page } from "$app/stores";
  // Fixed imports based on your file structure
  import FlightSearchForm from "./FlightSearchForm.svelte";
  import { flightStore } from "$lib/flights-commons/stores/flight-store.ts";
  import { cityUtils } from "$lib/flights-commons/utils/city-util.ts";

  //Session storage to store user selected cities
  let selectedCities = cityUtils.getInitialCities();

  // Form state that can be restored from query parameters
  let formState: {
    departure: string | null;
    passengers: number | null;
    class: string | null;
    adults: number | null;
    children: number | null;
    infants: number | null;
  } = {
    departure: null,
    passengers: null,
    class: null,
    adults: null,
    children: null,
    infants: null,
  };

  function handleNavigationState() {
    // Get the navigation state
    const state = history.state;
    NucleiLogger.logInfo("Navigation state:", "LANDING", state);

    if (state && state.selectionType && state.city) {
      NucleiLogger.logDebug("Processing navigation state:", "LANDING", state);

      const { selectionType, city } = state;

      // Update the appropriate city based on selection type
      if (selectionType === "Source") {
        selectedCities.from = {
          city: city.city,
          code: city.code,
          airport: city.airport,
        };
      } else if (selectionType === "Destination") {
        selectedCities.to = {
          city: city.city,
          code: city.code,
          airport: city.airport,
        };
      }

      // Trigger reactivity by reassigning the object
      selectedCities = { ...selectedCities };
      cityUtils.saveSelectedCities(selectedCities);

      // Clear the state to prevent reprocessing on refresh
      history.replaceState(null, "", window.location.href);
    }
  }

  function restoreFormStateFromQueryParams() {
    // Check if we have query parameters (coming back from listing page)
    const urlParams = $page.url.searchParams;

    if (
      urlParams.has("departure") ||
      urlParams.has("class") ||
      urlParams.has("passengers")
    ) {
      NucleiLogger.logInfo(
        "LANDING",
        "Restoring form state from query parameters"
      );

      // Restore cities from query params if available
      if (urlParams.has("fromCity")) {
        selectedCities.from = {
          city: urlParams.get("fromCity") || selectedCities.from.city,
          code: urlParams.get("fromCode") || selectedCities.from.code,
          airport: urlParams.get("fromAirport") || selectedCities.from.airport,
        };
      }

      if (urlParams.has("toCity")) {
        selectedCities.to = {
          city: urlParams.get("toCity") || selectedCities.to.city,
          code: urlParams.get("toCode") || selectedCities.to.code,
          airport: urlParams.get("toAirport") || selectedCities.to.airport,
        };
      }

      // Restore form state
      const passengersParam = urlParams.get("passengers");
      const adultsParam = urlParams.get("adults");
      const childrenParam = urlParams.get("children");
      const infantsParam = urlParams.get("infants");

      formState = {
        departure: urlParams.get("departure"),
        passengers: passengersParam ? parseInt(passengersParam) : null,
        class: urlParams.get("class"),
        adults: adultsParam ? parseInt(adultsParam) : null,
        children: childrenParam ? parseInt(childrenParam) : null,
        infants: infantsParam ? parseInt(infantsParam) : null,
      };

      // Update cities reactively
      selectedCities = { ...selectedCities };
      cityUtils.saveSelectedCities(selectedCities);

      NucleiLogger.logInfo("LANDING", "Form state restored:", formState);
    }
  }

  function handleCitySwap(event: any) {
    selectedCities = event.detail;
    cityUtils.saveSelectedCities(selectedCities);
  }

  function handleSearchFlights(event: any) {
    const searchData = event.detail;

    // Build query parameters including adults, children, infants
    const params = new URLSearchParams({
      fromCity: selectedCities.from.city,
      fromCode: selectedCities.from.code,
      fromAirport: selectedCities.from.airport,
      toCity: selectedCities.to.city,
      toCode: selectedCities.to.code,
      toAirport: selectedCities.to.airport,
      departure: searchData.departure,
      passengers: searchData.passengers.toString(),
      class: searchData.clas,
      adults: searchData.adults.toString(),
      children: searchData.children.toString(),
      infants: searchData.infants.toString(),
    });

    const finalUrl = `/flights-base/dev/flights/listing?${params.toString()}`;
    NucleiLogger.logInfo("LANDING", "Navigating to URL: ", finalUrl);

    NavigatorUtils.navigateTo({
      url: finalUrl,
    });
  }

  onMount(async () => {
    NucleiLogger.logInfo("LANDING", "Landing screen mounted");

    setLoadingLce();
    handleNavigationState();
    restoreFormStateFromQueryParams();
    await fetchScreenData();

    if (typeof window !== "undefined") {
      window.addEventListener("popstate", handleNavigationState);
    }
  });

  const fetchScreenData = async () => {
    setContentLce();
  };

  function handleRetry() {
    setLoadingLce();
    fetchScreenData();
  }
</script>

<AppBar title="Flights">
  <div slot="action">
    <div class="flex flex-row items-center gap-2">
      <LandingWalletCta />
      <ListCouponCta />
      <LandingRewardCta />
      <div class="dropdown dropdown-end"><ThreeDotMenu /></div>
    </div>
  </div>
</AppBar>

{#if $lceStore.isLoading}
  <div class="h-screen flex flex-col justify-center">
    <PrimaryLoader />
  </div>
{:else if $lceStore.hasError && $lceStore.errorDetails != null}
  <ErrorHandling
    errorHandling={$lceStore.errorDetails}
    on:submit={handleRetry}
  />
{:else if $lceStore.hasContent}
  <div class="flex-1 overflow-y-auto">
    <FlightSearchForm
      {selectedCities}
      {formState}
      on:citySwap={handleCitySwap}
      on:searchFlights={handleSearchFlights}
    />
  </div>
{/if}
