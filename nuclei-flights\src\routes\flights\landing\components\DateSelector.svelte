<script lang="ts">
	// Import Svelte's event dispatcher for sending custom events to parent components
	import { createEventDispatcher } from 'svelte';
	import { NucleiLogger } from '@CDNA-Technologies/svelte-vitals/logger';

	// Props received from parent
	export let departureDate;
	export let departureDay;
	export let returnDate;
	export let isReturnTrip;
	export let showCalendar;
	export let selectedDate;

	// Create the dispatcher for sending events
	const dispatch = createEventDispatcher();

	// Dispatch event to open calendar
	function openCalendar() {
		dispatch('openCalendar');
	}

	// Dispatch event to toggle return trip selection
	function toggleReturnTrip() {
		dispatch('toggleReturn');
	}

	// Dispatch event when a date is selected from the calendar
	function handleCalendarSelect(event) {
		dispatch('calendarSelect', event);
	}

	// Dispatch event when calendar is closed
	function handleCalendarClose(event) {
		dispatch('calendarClose', event);
	}
</script>

<!-- Departure and Return Dates -->
<div class="relative">
	<div class="w-full flex bg-white border border-gray-200 rounded-lg shadow-sm text-left mb-4">
		<div class="flex-1 relative">
			<div
				class="cursor-pointer w-full p-4 px-4 bg-white rounded-lg shadow-sm text-left hover:bg-gray-50 transition-colors duration-200"
				on:click|stopPropagation={openCalendar}
			>
				<div class="flex items-center">
					<div>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							width="24"
							height="25"
							viewBox="0 0 24 25"
							fill="none"
						>
							<path
								d="M2 22.5H22"
								stroke="#8A8A8A"
								stroke-width="2"
								stroke-linecap="round"
								stroke-linejoin="round"
							/>
							<path
								d="M3.77 11.27L2 9.5L4 5L5.1 5.55C5.65 5.83 6 6.39 6 7C6 7.61 6.35 8.17 6.9 8.45L8 9L11 3L12.05 3.53C12.3418 3.67534 12.5937 3.88981 12.7836 4.15473C12.9736 4.41965 13.096 4.72699 13.14 5.05L13.86 10.45C13.904 10.773 14.0264 11.0804 14.2164 11.3453C14.4063 11.6102 14.6582 11.8247 14.95 11.97L19.35 14.17C19.77 14.39 20.13 14.72 20.36 15.13L20.96 16.16C21.45 17.04 20.9 18.14 19.9 18.26L18.72 18.41C18.25 18.47 17.77 18.39 17.35 18.17L4.29 11.65C4.09728 11.5523 3.92167 11.4239 3.77 11.27Z"
								stroke="#8A8A8A"
								stroke-width="1.5"
								stroke-linecap="round"
								stroke-linejoin="round"
							/>
						</svg>
					</div>
					<div>
						<div class="select-none text-gray-500 pl-3 text-xs">Departure</div>
						<div class="select-none text-gray-900 pl-3 font-bold text-lg">{departureDate}</div>
						<div class="select-none text-gray-500 pl-3 text-xs">{departureDay}</div>
					</div>
				</div>
			</div>

			<!-- Calendar Component - positioned relative to this container -->
			<slot />
		</div>

		<div class="border-l border-gray-200 my-2" />
		<!-- Vertical divider -->

		<div
			class=" cursor-pointer flex-1 p-4 bg-white rounded-lg shadow-sm text-left px-4 hover:bg-gray-50 transition-colors duration-200"
			on:click={toggleReturnTrip}
		>
			<div class="flex items-center">
			
				<div>
					<div class="select-none text-gray-500 pl-3 text-xs">Return</div>
					{#if isReturnTrip && returnDate}
						<div class="select-none text-gray-900 pl-3 font-bold text-lg">{returnDate}</div>
						<div class="select-none text-gray-500 pl-3 text-xs">sunday</div>
					{:else}
						<div class="select-none text-blue-600 pl-3 font-bold text-lg">Add Return</div>
						<div class="select-none text-gray-500 pl-3 text-xs">and save more!</div>
					{/if}
				</div>
					<div  class="w-24px h-24px pl-3">
					<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
<path d="M11.832 2.5C17.2538 2.5 21.6648 6.91104 21.665 12.332C21.665 17.7532 17.254 22.165 11.832 22.165C6.41027 22.1648 2 17.7538 2 12.332C2.00025 6.91043 6.41043 2.50025 11.832 2.5ZM11.832 4.02344C7.25126 4.02369 3.52369 7.75049 3.52344 12.332C3.52344 16.9138 7.25111 20.6413 11.832 20.6416C16.4139 20.6416 20.1416 16.9139 20.1416 12.332C20.1413 7.75034 16.413 4.02344 11.832 4.02344Z" fill="#1BA4F7"/>
<path d="M15.6395 11.5034H12.5931V8.45695C12.5931 8.03653 12.2526 7.69531 11.8314 7.69531C11.4103 7.69531 11.0698 8.03653 11.0698 8.45695V11.5034H8.02335C7.60217 11.5034 7.26172 11.8446 7.26172 12.265C7.26172 12.6855 7.60217 13.0267 8.02335 13.0267H11.0698V16.0731C11.0698 16.4936 11.4103 16.8348 11.8314 16.8348C12.2526 16.8348 12.5931 16.4936 12.5931 16.0731V13.0267H15.6395C16.0607 13.0267 16.4012 12.6855 16.4012 12.265C16.4012 11.8446 16.0607 11.5034 15.6395 11.5034Z" fill="#1BA4F7"/>
</svg>
				</div>
			</div>
		</div>
	</div>
</div>
